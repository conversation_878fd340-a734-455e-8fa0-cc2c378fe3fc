/**
 * Manual Review Interface
 * 
 * Provides interface for manual editorial review with exact format
 * requirements for editorial text and comprehensive review workflow.
 */

import { EditorialControls } from './editorial-controls';
import { ContentValidator } from './validator';
import { QualityScorer } from './quality-scorer';
import { supabase } from '../supabase';
import { 
  EditorialReview, 
  ReviewStatus, 
  ManualReviewData,
  ReviewDecision,
  EditorialTextValidation
} from '../types';

export interface ManualReviewRequest {
  toolId: string;
  reviewerId: string;
  decision: ReviewDecision;
  reviewNotes?: string;
  featuredDate?: string;
  editorialText?: string;
  contentModifications?: any;
  qualityOverride?: number;
}

export interface ReviewPreviewData {
  tool: any;
  generatedContent: any;
  currentReview?: EditorialReview;
  validationResult: any;
  qualityScore: any;
  suggestedEditorialText: string;
  reviewHistory: any[];
}

export interface EditorialTextPreview {
  text: string;
  isValid: boolean;
  errors: string[];
  formattedDate: string;
  template: string;
}

export class ManualReview {
  private editorialControls: EditorialControls;
  private validator: ContentValidator;
  private qualityScorer: QualityScorer;

  constructor() {
    this.editorialControls = new EditorialControls();
    this.validator = new ContentValidator();
    this.qualityScorer = new QualityScorer();
  }

  /**
   * Get comprehensive review data for a tool
   */
  async getReviewData(toolId: string): Promise<ReviewPreviewData> {
    try {
      // Get tool data with editorial review
      const { data: tool, error: toolError } = await supabase
        .from('tools')
        .select(`
          *,
          editorial_reviews (*)
        `)
        .eq('id', toolId)
        .single();

      if (toolError || !tool) {
        throw new Error(`Tool not found: ${toolError?.message || 'Unknown error'}`);
      }

      // Get current review if exists
      const currentReview = tool.editorial_reviews?.[0];

      // Validate generated content
      const validationResult = await this.validator.validateContent(
        tool.generated_content,
        tool.scraped_data?.content || '',
        tool.website_url
      );

      // Score content quality
      const qualityScore = await this.qualityScorer.scoreContent(
        tool.generated_content,
        validationResult
      );

      // Generate suggested editorial text
      const suggestedDate = currentReview?.featured_date || new Date().toISOString().split('T')[0];
      const suggestedEditorialText = this.editorialControls.generateEditorialText(suggestedDate);

      // Get review history
      const reviewHistory = await this.getReviewHistory(toolId);

      return {
        tool,
        generatedContent: tool.generated_content,
        currentReview: currentReview ? this.mapDatabaseToEditorialReview(currentReview) : undefined,
        validationResult,
        qualityScore,
        suggestedEditorialText,
        reviewHistory
      };

    } catch (error: any) {
      console.error('Failed to get review data:', error);
      throw error;
    }
  }

  /**
   * Submit manual review decision
   */
  async submitReview(request: ManualReviewRequest): Promise<EditorialReview> {
    try {
      // Validate editorial text if provided
      if (request.editorialText) {
        this.validateEditorialText(request.editorialText);
      }

      // Get or create editorial review
      let reviewId: string;
      const existingReview = await this.getExistingReview(request.toolId);
      
      if (existingReview) {
        reviewId = existingReview.id;
      } else {
        // Create new review
        const newReview = await this.editorialControls.createReview({
          toolId: request.toolId,
          generatedContent: {}, // Will be populated from tool data
          qualityScore: request.qualityOverride || 0,
          priority: 'normal'
        });
        reviewId = newReview.id;
      }

      // Update review with decision
      const updatedReview = await this.editorialControls.updateReview({
        reviewId,
        reviewedBy: request.reviewerId,
        status: this.mapDecisionToStatus(request.decision),
        reviewNotes: request.reviewNotes,
        featuredDate: request.featuredDate,
        editorialText: request.editorialText,
        qualityScore: request.qualityOverride
      });

      // Apply content modifications if provided
      if (request.contentModifications) {
        await this.applyContentModifications(request.toolId, request.contentModifications);
      }

      // Log review action
      await this.logReviewAction(request);

      return updatedReview;

    } catch (error: any) {
      console.error('Failed to submit review:', error);
      throw error;
    }
  }

  /**
   * Preview editorial text with validation
   */
  previewEditorialText(featuredDate: string): EditorialTextPreview {
    try {
      const text = this.editorialControls.generateEditorialText(featuredDate);
      const isValid = this.editorialControls.validateEditorialTextFormat(text);
      const template = this.editorialControls.getEditorialTextTemplate();

      return {
        text,
        isValid,
        errors: [],
        formattedDate: this.formatDateForPreview(featuredDate),
        template: template.requiredFormat
      };

    } catch (error: any) {
      return {
        text: '',
        isValid: false,
        errors: [error.message],
        formattedDate: '',
        template: this.editorialControls.getEditorialTextTemplate().requiredFormat
      };
    }
  }

  /**
   * Validate custom editorial text
   */
  validateEditorialText(editorialText: string): EditorialTextValidation {
    try {
      const isValid = this.editorialControls.validateEditorialTextFormat(editorialText);
      const template = this.editorialControls.getEditorialTextTemplate();

      return {
        isValid,
        errors: [],
        template: template.requiredFormat,
        example: template.example
      };

    } catch (error: any) {
      return {
        isValid: false,
        errors: [error.message],
        template: this.editorialControls.getEditorialTextTemplate().requiredFormat,
        example: this.editorialControls.getEditorialTextTemplate().example
      };
    }
  }

  /**
   * Get review queue for admin interface
   */
  async getReviewQueue(filters?: {
    status?: ReviewStatus;
    reviewer?: string;
    priority?: string;
    dateRange?: { start: string; end: string };
  }): Promise<ManualReviewData[]> {
    try {
      let query = supabase
        .from('editorial_reviews')
        .select(`
          *,
          tools (*)
        `)
        .order('created_at', { ascending: true });

      // Apply filters
      if (filters?.status) {
        query = query.eq('review_status', filters.status);
      }
      if (filters?.reviewer) {
        query = query.eq('reviewed_by', filters.reviewer);
      }
      if (filters?.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.start)
          .lte('created_at', filters.dateRange.end);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to get review queue: ${error.message}`);
      }

      return data.map(review => ({
        review: this.mapDatabaseToEditorialReview(review),
        tool: review.tools,
        priority: this.determinePriority(review),
        estimatedReviewTime: this.estimateReviewTime(review),
        flags: review.content_flags || []
      }));

    } catch (error: any) {
      console.error('Failed to get review queue:', error);
      throw error;
    }
  }

  /**
   * Bulk approve tools with editorial text
   */
  async bulkApprove(
    toolIds: string[],
    reviewerId: string,
    featuredDate: string,
    notes?: string
  ): Promise<{ success: string[]; failed: string[] }> {
    const results: { success: string[]; failed: string[] } = { success: [], failed: [] };
    const editorialText = this.editorialControls.generateEditorialText(featuredDate);

    for (const toolId of toolIds) {
      try {
        await this.submitReview({
          toolId,
          reviewerId,
          decision: 'approve',
          reviewNotes: notes,
          featuredDate,
          editorialText
        });
        results.success.push(toolId);
      } catch (error) {
        console.error(`Failed to approve tool ${toolId}:`, error);
        results.failed.push(toolId);
      }
    }

    return results;
  }

  /**
   * Get existing review for a tool
   */
  private async getExistingReview(toolId: string): Promise<EditorialReview | null> {
    const { data, error } = await supabase
      .from('editorial_reviews')
      .select('*')
      .eq('tool_id', toolId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error || !data) {
      return null;
    }

    return this.mapDatabaseToEditorialReview(data);
  }

  /**
   * Get review history for a tool
   */
  private async getReviewHistory(toolId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('editorial_reviews')
      .select('*')
      .eq('tool_id', toolId)
      .order('created_at', { ascending: false });

    if (error) {
      return [];
    }

    return data.map(review => this.mapDatabaseToEditorialReview(review));
  }

  /**
   * Apply content modifications to tool
   */
  private async applyContentModifications(toolId: string, modifications: any): Promise<void> {
    const { error } = await supabase
      .from('tools')
      .update({
        generated_content: modifications,
        updated_at: new Date().toISOString()
      })
      .eq('id', toolId);

    if (error) {
      throw new Error(`Failed to apply content modifications: ${error.message}`);
    }
  }

  /**
   * Log review action for audit trail
   */
  private async logReviewAction(request: ManualReviewRequest): Promise<void> {
    // This would typically log to an audit table
    console.log('Review action logged:', {
      toolId: request.toolId,
      reviewerId: request.reviewerId,
      decision: request.decision,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Map review decision to status
   */
  private mapDecisionToStatus(decision: ReviewDecision): ReviewStatus {
    switch (decision) {
      case 'approve': return 'approved';
      case 'reject': return 'rejected';
      case 'request_changes': return 'needs_revision';
      default: return 'pending';
    }
  }

  /**
   * Determine review priority
   */
  private determinePriority(review: any): 'high' | 'normal' | 'low' {
    // Logic to determine priority based on various factors
    if (review.quality_score < 60) return 'high';
    if (review.content_flags && review.content_flags.length > 3) return 'high';
    return 'normal';
  }

  /**
   * Estimate review time
   */
  private estimateReviewTime(review: any): number {
    // Estimate in minutes based on content complexity
    let baseTime = 15; // Base 15 minutes
    
    if (review.content_flags && review.content_flags.length > 0) {
      baseTime += review.content_flags.length * 5;
    }
    
    if (review.quality_score < 70) {
      baseTime += 10;
    }
    
    return baseTime;
  }

  /**
   * Format date for preview
   */
  private formatDateForPreview(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Map database record to EditorialReview interface
   */
  private mapDatabaseToEditorialReview(data: any): EditorialReview {
    return {
      id: data.id,
      tool_id: data.tool_id,
      reviewed_by: data.reviewed_by,
      review_status: data.review_status,
      review_date: data.review_date,
      featured_date: data.featured_date,
      review_notes: data.review_notes,
      editorial_text: data.editorial_text,
      quality_score: data.quality_score,
      content_flags: data.content_flags || [],
      approval_workflow: data.approval_workflow,
      created_at: data.created_at,
      updated_at: data.updated_at
    };
  }
}
