/**
 * Editorial Controls System
 * 
 * Manages the editorial workflow including review assignment,
 * approval processes, and editorial text management with
 * EXACT format requirements.
 */

import { supabase } from '../supabase';
import { 
  EditorialReview, 
  ReviewStatus, 
  EditorialWorkflowData,
  ApprovalWorkflow,
  ValidationResult,
  QualityScore
} from '../types';

export interface CreateReviewRequest {
  toolId: string;
  generatedContent: any;
  qualityScore: number;
  validationResult?: ValidationResult;
  priority: 'high' | 'normal' | 'low';
  assignedReviewer?: string;
}

export interface ApprovalRequest {
  reviewId: string;
  reviewedBy: string;
  status: ReviewStatus;
  reviewNotes?: string;
  featuredDate?: string;
  editorialText?: string;
  qualityScore?: number;
}

export interface EditorialTextTemplate {
  template: string;
  requiredFormat: string;
  example: string;
}

export class EditorialControls {
  private static readonly EDITORIAL_TEXT_TEMPLATE: EditorialTextTemplate = {
    template: "was manually vetted by our editorial team and was first featured on {date}",
    requiredFormat: "was manually vetted by our editorial team and was first featured on [Month Day, Year]",
    example: "was manually vetted by our editorial team and was first featured on August 7th, 2024"
  };

  /**
   * Create a new editorial review
   */
  async createReview(request: CreateReviewRequest): Promise<EditorialReview> {
    try {
      const reviewData = {
        tool_id: request.toolId,
        reviewed_by: request.assignedReviewer || 'system',
        review_status: 'pending' as ReviewStatus,
        review_date: new Date().toISOString().split('T')[0],
        quality_score: request.qualityScore,
        content_flags: this.extractContentFlags(request.validationResult),
        approval_workflow: this.initializeWorkflow(request.priority),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('editorial_reviews')
        .insert(reviewData)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create editorial review: ${error.message}`);
      }

      return this.mapDatabaseToEditorialReview(data);

    } catch (error: any) {
      console.error('Failed to create editorial review:', error);
      throw error;
    }
  }

  /**
   * Update editorial review with approval/rejection
   */
  async updateReview(request: ApprovalRequest): Promise<EditorialReview> {
    try {
      // Validate editorial text format if provided
      if (request.editorialText) {
        this.validateEditorialTextFormat(request.editorialText);
      }

      const updateData: any = {
        review_status: request.status,
        reviewed_by: request.reviewedBy,
        review_notes: request.reviewNotes,
        updated_at: new Date().toISOString()
      };

      // Add featured date if provided
      if (request.featuredDate) {
        updateData.featured_date = request.featuredDate;
      }

      // Add editorial text if provided
      if (request.editorialText) {
        updateData.editorial_text = request.editorialText;
      }

      // Update quality score if provided
      if (request.qualityScore) {
        updateData.quality_score = request.qualityScore;
      }

      // Update approval workflow
      const { data: currentReview } = await supabase
        .from('editorial_reviews')
        .select('approval_workflow')
        .eq('id', request.reviewId)
        .single();

      if (currentReview) {
        updateData.approval_workflow = this.updateWorkflowHistory(
          currentReview.approval_workflow,
          request.status,
          request.reviewedBy,
          request.reviewNotes
        );
      }

      const { data, error } = await supabase
        .from('editorial_reviews')
        .update(updateData)
        .eq('id', request.reviewId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update editorial review: ${error.message}`);
      }

      // Update tool status if approved
      if (request.status === 'approved') {
        await this.updateToolStatus(data.tool_id, 'approved', request.editorialText);
      }

      return this.mapDatabaseToEditorialReview(data);

    } catch (error: any) {
      console.error('Failed to update editorial review:', error);
      throw error;
    }
  }

  /**
   * Get editorial workflow data for admin interface
   */
  async getWorkflowData(): Promise<EditorialWorkflowData> {
    try {
      // Get pending reviews
      const { data: pendingReviews, error: pendingError } = await supabase
        .from('editorial_reviews')
        .select(`
          *,
          tools (*)
        `)
        .eq('review_status', 'pending')
        .order('created_at', { ascending: true });

      if (pendingError) {
        throw new Error(`Failed to fetch pending reviews: ${pendingError.message}`);
      }

      // Get recent reviews
      const { data: recentReviews, error: recentError } = await supabase
        .from('editorial_reviews')
        .select('*')
        .neq('review_status', 'pending')
        .order('updated_at', { ascending: false })
        .limit(20);

      if (recentError) {
        throw new Error(`Failed to fetch recent reviews: ${recentError.message}`);
      }

      // Calculate stats
      const stats = await this.calculateReviewStats();

      return {
        pendingReviews: pendingReviews.map(review => ({
          ...review.tools,
          editorialReview: this.mapDatabaseToEditorialReview(review)
        })),
        recentReviews: recentReviews.map(review => this.mapDatabaseToEditorialReview(review)),
        reviewStats: stats,
        workflowSettings: {
          autoApprovalThreshold: 85,
          requiredReviewers: 1,
          escalationRules: []
        }
      };

    } catch (error: any) {
      console.error('Failed to get workflow data:', error);
      throw error;
    }
  }

  /**
   * Generate editorial text with exact format
   */
  generateEditorialText(featuredDate: string): string {
    // Validate date format and convert to required format
    const date = new Date(featuredDate);
    const formattedDate = this.formatDateForEditorial(date);
    
    return EditorialControls.EDITORIAL_TEXT_TEMPLATE.template.replace('{date}', formattedDate);
  }

  /**
   * Validate editorial text format
   */
  validateEditorialTextFormat(editorialText: string): boolean {
    const requiredPattern = /was manually vetted by our editorial team and was first featured on [A-Za-z]+ \d{1,2}(st|nd|rd|th), \d{4}/;
    
    if (!requiredPattern.test(editorialText)) {
      throw new Error(
        `Editorial text must follow exact format: "${EditorialControls.EDITORIAL_TEXT_TEMPLATE.requiredFormat}". ` +
        `Example: "${EditorialControls.EDITORIAL_TEXT_TEMPLATE.example}"`
      );
    }
    
    return true;
  }

  /**
   * Get editorial text template information
   */
  getEditorialTextTemplate(): EditorialTextTemplate {
    return EditorialControls.EDITORIAL_TEXT_TEMPLATE;
  }

  /**
   * Extract content flags from validation result
   */
  private extractContentFlags(validationResult?: ValidationResult): string[] {
    if (!validationResult) return [];
    
    const flags: string[] = [];
    
    // Add error flags
    validationResult.errors.forEach(error => {
      flags.push(`error:${error.field}:${error.message}`);
    });
    
    // Add warning flags
    validationResult.warnings.forEach(warning => {
      flags.push(`warning:${warning}`);
    });
    
    return flags;
  }

  /**
   * Initialize approval workflow
   */
  private initializeWorkflow(priority: string): ApprovalWorkflow {
    return {
      currentStep: 'editorial_review',
      history: [{
        step: 'created',
        timestamp: new Date().toISOString(),
        user: 'system',
        notes: `Review created with ${priority} priority`
      }]
    };
  }

  /**
   * Update workflow history
   */
  private updateWorkflowHistory(
    currentWorkflow: ApprovalWorkflow,
    status: ReviewStatus,
    user: string,
    notes?: string
  ): ApprovalWorkflow {
    const newStep = status === 'approved' ? 'approved' : 
                   status === 'rejected' ? 'rejected' : 'needs_revision';
    
    return {
      currentStep: newStep,
      history: [
        ...currentWorkflow.history,
        {
          step: newStep,
          timestamp: new Date().toISOString(),
          user,
          notes: notes || `Review ${status}`
        }
      ]
    };
  }

  /**
   * Update tool status after approval
   */
  private async updateToolStatus(
    toolId: string, 
    status: string, 
    editorialText?: string
  ): Promise<void> {
    const updateData: any = {
      content_status: status === 'approved' ? 'published' : 'draft',
      updated_at: new Date().toISOString()
    };

    if (status === 'approved') {
      updateData.published_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('tools')
      .update(updateData)
      .eq('id', toolId);

    if (error) {
      throw new Error(`Failed to update tool status: ${error.message}`);
    }
  }

  /**
   * Calculate review statistics
   */
  private async calculateReviewStats(): Promise<{
    totalReviews: number;
    approvalRate: number;
    averageReviewTime: number;
    featuredTools: number;
  }> {
    const { data: reviews, error } = await supabase
      .from('editorial_reviews')
      .select('review_status, created_at, updated_at, featured_date');

    if (error) {
      throw new Error(`Failed to calculate stats: ${error.message}`);
    }

    const totalReviews = reviews.length;
    const approvedReviews = reviews.filter(r => r.review_status === 'approved').length;
    const approvalRate = totalReviews > 0 ? (approvedReviews / totalReviews) * 100 : 0;
    
    // Calculate average review time
    const completedReviews = reviews.filter(r => r.review_status !== 'pending');
    const totalReviewTime = completedReviews.reduce((sum, review) => {
      const created = new Date(review.created_at).getTime();
      const updated = new Date(review.updated_at).getTime();
      return sum + (updated - created);
    }, 0);
    
    const averageReviewTime = completedReviews.length > 0 ? 
      totalReviewTime / completedReviews.length / (1000 * 60 * 60) : 0; // Convert to hours
    
    const featuredTools = reviews.filter(r => r.featured_date).length;

    return {
      totalReviews,
      approvalRate: Math.round(approvalRate),
      averageReviewTime: Math.round(averageReviewTime * 10) / 10,
      featuredTools
    };
  }

  /**
   * Format date for editorial text
   */
  private formatDateForEditorial(date: Date): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const month = months[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    
    // Add ordinal suffix
    const suffix = this.getOrdinalSuffix(day);
    
    return `${month} ${day}${suffix}, ${year}`;
  }

  /**
   * Get ordinal suffix for day
   */
  private getOrdinalSuffix(day: number): string {
    if (day >= 11 && day <= 13) {
      return 'th';
    }
    
    switch (day % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  }

  /**
   * Map database record to EditorialReview interface
   */
  private mapDatabaseToEditorialReview(data: any): EditorialReview {
    return {
      id: data.id,
      tool_id: data.tool_id,
      reviewed_by: data.reviewed_by,
      review_status: data.review_status,
      review_date: data.review_date,
      featured_date: data.featured_date,
      review_notes: data.review_notes,
      editorial_text: data.editorial_text,
      quality_score: data.quality_score,
      content_flags: data.content_flags || [],
      approval_workflow: data.approval_workflow,
      created_at: data.created_at,
      updated_at: data.updated_at
    };
  }
}
